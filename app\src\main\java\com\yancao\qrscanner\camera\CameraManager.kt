package com.yancao.qrscanner.camera

import android.content.Context
import android.util.Size
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.core.UseCase
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.common.Barcode
import java.util.concurrent.Executors

@ExperimentalGetImage
class CameraManager(private val context: Context) {

    private var cameraProvider: ProcessCameraProvider? = null
    private val cameraExecutor = Executors.newSingleThreadExecutor()

    // 新增：图像分析器用于实时二维码扫描
    private var imageAnalyzer: ImageAnalysis? = null
    private var qrAnalyzer: RealtimeQRAnalyzer? = null

    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false,
        onQRCodeDetected: ((List<String>) -> Unit)? = null,
        // 新增：二维码位置信息回调
        onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
    ) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()

            val analysisResolution = Size(1152, 2016)

            // 创建自定义分辨率过滤器
            val customResolutionFilter = ResolutionFilter { supportedSizes, rotationDegrees ->
                println("CameraManager - 支持的分辨率列表:")
                supportedSizes.forEach { size ->
                    println("  ${size.width}x${size.height}")
                }
                println("CameraManager - 目标分辨率: ${analysisResolution.width}x${analysisResolution.height}")
                println("CameraManager - 旋转角度: $rotationDegrees")

                // 寻找最接近目标分辨率的尺寸
                val bestMatch = supportedSizes.minByOrNull { size ->
                    val widthDiff = abs(size.width - analysisResolution.width)
                    val heightDiff = abs(size.height - analysisResolution.height)
                    widthDiff + heightDiff
                }

                println("CameraManager - 选择的分辨率: ${bestMatch?.width}x${bestMatch?.height}")

                // 如果找到精确匹配，优先返回；否则返回最接近的
                if (supportedSizes.contains(analysisResolution)) {
                    listOf(analysisResolution)
                } else if (bestMatch != null) {
                    listOf(bestMatch)
                } else {
                    supportedSizes.take(1) // 备用方案
                }
            }

            val resolutionSelector = ResolutionSelector.Builder()
                .setResolutionStrategy(
                    ResolutionStrategy(
                        analysisResolution, // 首选分辨率
                        ResolutionStrategy.FALLBACK_RULE_CLOSEST_HIGHER_THEN_LOWER // 修改回退策略
                    )
                )
                .setResolutionFilter(customResolutionFilter) // 添加自定义过滤器
                .build()

            // Preview配置
            val preview = Preview.Builder()
                .setResolutionSelector(resolutionSelector)
                .build()
                .also {
                    it.surfaceProvider = previewView.surfaceProvider
                }

            // 新增：如果启用实时扫描，创建图像分析器
            val useCases = mutableListOf<UseCase>(preview)

            if (enableRealtimeScanning) {
                qrAnalyzer = RealtimeQRAnalyzer(onQRCodeDetected, onQRCodeWithPosition)
                imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .setResolutionSelector(resolutionSelector) // 使用相同的分辨率选择器
                    .build()
                    .also {
                        it.setAnalyzer(cameraExecutor, qrAnalyzer!!)
                    }
                useCases.add(imageAnalyzer!!)
            }

            try {
                cameraProvider?.unbindAll()
                cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    CameraSelector.DEFAULT_BACK_CAMERA,
                    *useCases.toTypedArray() // 绑定所有用例
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }, ContextCompat.getMainExecutor(context))
    }


    fun shutdown() {
        cameraExecutor.shutdown()
    }
}