/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/yancao/qrscanner/QrScanRepository.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt@ ?app/src/main/java/com/yancao/qrscanner/camera/QRCodeAnalyzer.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.kt= <app/src/main/java/com/yancao/qrscanner/ui/ResultsActivity.ktA @app/src/main/java/com/yancao/qrscanner/utils/BitmapSizeReduce.kt= <app/src/main/java/com/yancao/qrscanner/utils/DisplayUtils.kt< ;app/src/main/java/com/yancao/qrscanner/utils/ImageHolder.ktB Aapp/src/main/java/com/yancao/qrscanner/utils/RequestPermission.ktD Capp/src/main/java/com/yancao/qrscanner/viewModel/QrScanViewModel.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.ktD Capp/src/main/java/com/yancao/qrscanner/camera/RealtimeQRAnalyzer.ktC Bapp/src/main/java/com/yancao/qrscanner/domain/ScanResultsHolder.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.kt= <app/src/main/java/com/yancao/qrscanner/ui/ResultsActivity.kt? >app/src/main/java/com/yancao/qrscanner/utils/BroadCastUtils.ktD Capp/src/main/java/com/yancao/qrscanner/viewModel/QrScanViewModel.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.ktD Capp/src/main/java/com/yancao/qrscanner/camera/RealtimeQRAnalyzer.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.ktD Capp/src/main/java/com/yancao/qrscanner/viewModel/QrScanViewModel.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.ktD Capp/src/main/java/com/yancao/qrscanner/camera/RealtimeQRAnalyzer.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.ktD Capp/src/main/java/com/yancao/qrscanner/viewModel/QrScanViewModel.ktD Capp/src/main/java/com/yancao/qrscanner/viewModel/QrScanViewModel.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/ui/QrCodeOverlayView.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt? >app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.ktB Aapp/src/main/java/com/yancao/qrscanner/utils/RequestPermission.kt< ;app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.ktB Aapp/src/main/java/com/yancao/qrscanner/utils/RequestPermission.kt