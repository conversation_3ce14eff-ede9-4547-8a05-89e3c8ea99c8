package com.yancao.qrscanner.ui

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import com.google.mlkit.vision.barcode.common.Barcode

/**
 * 二维码覆盖层视图
 * 直接使用MLKit提供的位置信息绘制绿框
 */
class QrCodeOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关的画笔和样式
    private val scannedQRPaint = Paint().apply {
        color = Color.GREEN
        style = Paint.Style.STROKE
        strokeWidth = 8f
        isAntiAlias = true
    }

    private val textPaint = Paint().apply {
        color = Color.GREEN
        textSize = 28f
        isAntiAlias = true
        typeface = Typeface.DEFAULT_BOLD
    }

    // 存储当前检测到的二维码信息
    private data class DetectedQRInfo(
        val barcode: Barcode,
        val scaleFactor: Float,
        val offsetX: Float,
        val offsetY: Float
    )

    // 当前检测到的二维码列表
    private val detectedQRCodes = mutableListOf<DetectedQRInfo>()

    // 绘制控制变量
    private var frameCounter = 0
    private var drawFrequency = 3 // 默认每3帧绘制一次

    /**
     * 设置绘制频率
     */
    fun setDrawFrequency(frequency: Int) {
        drawFrequency = if (frequency > 0) frequency else 1
    }

    /**
     * 直接使用MLKit的Barcode对象更新二维码信息
     * @param barcodes MLKit检测到的二维码列表
     * @param imageWidth 原始图像宽度
     * @param imageHeight 原始图像高度
     */
    fun updateDetectedQRCodes(
        barcodes: List<Barcode>,
        imageWidth: Int,
        imageHeight: Int
    ) {
        // 清空当前列表
        detectedQRCodes.clear()

        // 获取当前视图尺寸
        val viewWidth = width
        val viewHeight = height

        if (viewWidth <= 0 || viewHeight <= 0 || imageWidth <= 0 || imageHeight <= 0) {
            invalidate()
            return
        }

        // 计算缩放因子和偏移量
        val scaleX = viewWidth.toFloat() / imageWidth.toFloat()
        val scaleY = viewHeight.toFloat() / imageHeight.toFloat()

        // 使用较小的缩放因子保持宽高比
        val scaleFactor = minOf(scaleX, scaleY)

        // 计算居中偏移
        val scaledImageWidth = imageWidth * scaleFactor
        val scaledImageHeight = imageHeight * scaleFactor
        val offsetX = (viewWidth - scaledImageWidth) / 2f
        val offsetY = (viewHeight - scaledImageHeight) / 2f

        // 添加调试日志
        Log.d("QROverlay", "=== 坐标转换调试信息 ===")
        Log.d("QROverlay", "图像尺寸: ${imageWidth}×${imageHeight}")
        Log.d("QROverlay", "视图尺寸: ${viewWidth}×${viewHeight}")
        Log.d("QROverlay", "缩放比例: scaleX=$scaleX, scaleY=$scaleY")
        Log.d("QROverlay", "选择缩放因子: $scaleFactor")
        Log.d("QROverlay", "缩放后图像尺寸: ${scaledImageWidth}×${scaledImageHeight}")
        Log.d("QROverlay", "偏移量: offsetX=$offsetX, offsetY=$offsetY")


        // 处理每个检测到的二维码
        barcodes.forEachIndexed { index, barcode ->
            barcode.boundingBox?.let { boundingBox ->
                // 原始坐标（基于ImageAnalysis图像）
                val originalLeft = boundingBox.left.toFloat()
                val originalTop = boundingBox.top.toFloat()
                val originalRight = boundingBox.right.toFloat()
                val originalBottom = boundingBox.bottom.toFloat()

                // 转换到View坐标系
                val transformedLeft = originalLeft * scaleFactor + offsetX
                val transformedTop = originalTop * scaleFactor + offsetY
                val transformedRight = originalRight * scaleFactor + offsetX
                val transformedBottom = originalBottom * scaleFactor + offsetY

                // 详细的转换日志
                Log.d("QROverlay", "--- 二维码 $index 坐标转换 ---")
                Log.d("QROverlay", "原始坐标: ($originalLeft, $originalTop, $originalRight, $originalBottom)")
                Log.d("QROverlay", "转换后坐标: ($transformedLeft, $transformedTop, $transformedRight, $transformedBottom)")
                Log.d("QROverlay", "二维码内容: ${barcode.rawValue}")

                detectedQRCodes.add(
                    DetectedQRInfo(
                        barcode = barcode,
                        scaleFactor = scaleFactor,
                        offsetX = offsetX,
                        offsetY = offsetY
                    )
                )
            }
        }

        // 处理每个检测到的二维码
//        barcodes.forEach { barcode ->
//            detectedQRCodes.add(
//                DetectedQRInfo(
//                    barcode = barcode,
//                    scaleFactor = scaleFactor,
//                    offsetX = offsetX,
//                    offsetY = offsetY
//                )
//            )
//        }

        // 标记View为"需要重绘"；请求Android系统在下一个绘制周期重绘这个View
        invalidate()
    }

    /**
     * 清除所有二维码标识
     */
    fun clearDetectedQRCodes() {
        detectedQRCodes.clear()
        invalidate()
    }


    /**
     * 每个绘制周期系统都会调度onDraw函数，这里执行了覆盖写法。
     */
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 控制绘制频率
        frameCounter++
        if (frameCounter % drawFrequency != 0) {
            return
        }

        // 绘制所有检测到的二维码
        detectedQRCodes.forEach { qrInfo ->
            drawQRCodeFromBarcode(canvas, qrInfo)
        }
    }

    /**
     * 直接从Barcode对象绘制二维码框
     */
    private fun drawQRCodeFromBarcode(canvas: Canvas, qrInfo: DetectedQRInfo) {
        val barcode = qrInfo.barcode

        barcode.boundingBox?.let { boundingBox ->
            // 使用存储的缩放因子和偏移量进行转换
            val scaledRect = RectF(
                boundingBox.left * qrInfo.scaleFactor + qrInfo.offsetX,
                boundingBox.top * qrInfo.scaleFactor + qrInfo.offsetY,
                boundingBox.right * qrInfo.scaleFactor + qrInfo.offsetX,
                boundingBox.bottom * qrInfo.scaleFactor + qrInfo.offsetY
            )

            // 绘制前的验证日志
            Log.d("QROverlay", "绘制绿框: ${scaledRect.left}, ${scaledRect.top}, ${scaledRect.right}, ${scaledRect.bottom}")
            Log.d("QROverlay", "绿框尺寸: 宽=${scaledRect.width()}, 高=${scaledRect.height()}")

            // 确保绿框在View范围内
            if (scaledRect.left >= 0 && scaledRect.top >= 0 &&
                scaledRect.right <= width && scaledRect.bottom <= height) {
                drawQRFrame(canvas, scaledRect, barcode.rawValue ?: "")
                Log.d("QROverlay", "✅ 绿框绘制成功")
            } else {
                Log.w("QROverlay", "⚠️ 绿框超出View范围，跳过绘制")
            }
        }

//        barcode.boundingBox?.let { boundingBox ->
//            val scaledRect = RectF(
//                boundingBox.left * qrInfo.scaleFactor + qrInfo.offsetX,
//                boundingBox.top * qrInfo.scaleFactor + qrInfo.offsetY,
//                boundingBox.right * qrInfo.scaleFactor + qrInfo.offsetX,
//                boundingBox.bottom * qrInfo.scaleFactor + qrInfo.offsetY
//            )
//
//            drawQRFrame(canvas, scaledRect, barcode.rawValue ?: "")
//        }
    }

    /**
     * 绘制矩形二维码框
     */
    private fun drawQRFrame(canvas: Canvas, rect: RectF, content: String) {
        // 绘制主边框
        canvas.drawRect(rect, scannedQRPaint)

        // 绘制四个角的加强标识
        val cornerLength = minOf(rect.width(), rect.height()) * 0.15f
        val cornerStroke = 6f

        val cornerPaint = Paint().apply {
            color = Color.GREEN
            style = Paint.Style.STROKE
            strokeWidth = cornerStroke
            isAntiAlias = true
        }

        // 左上角
        canvas.drawLine(rect.left, rect.top, rect.left + cornerLength, rect.top, cornerPaint)
        canvas.drawLine(rect.left, rect.top, rect.left, rect.top + cornerLength, cornerPaint)

        // 右上角
        canvas.drawLine(rect.right, rect.top, rect.right - cornerLength, rect.top, cornerPaint)
        canvas.drawLine(rect.right, rect.top, rect.right, rect.top + cornerLength, cornerPaint)

        // 左下角
        canvas.drawLine(rect.left, rect.bottom, rect.left + cornerLength, rect.bottom, cornerPaint)
        canvas.drawLine(rect.left, rect.bottom, rect.left, rect.bottom - cornerLength, cornerPaint)

        // 右下角
        canvas.drawLine(rect.right, rect.bottom, rect.right - cornerLength, rect.bottom, cornerPaint)
        canvas.drawLine(rect.right, rect.bottom, rect.right, rect.bottom - cornerLength, cornerPaint)

        // 绘制标识文字
        drawTextNearPoint(canvas, rect.left, rect.top - 15f, "✓ 已扫描")
    }

    /**
     * 在指定位置附近绘制文字
     */
    private fun drawTextNearPoint(canvas: Canvas, x: Float, y: Float, text: String) {
        if (y > 0 && x >= 0) {
            // 绘制文字背景
            val textBounds = Rect()
            textPaint.getTextBounds(text, 0, text.length, textBounds)
            val backgroundRect = RectF(
                x - 8f,
                y - textBounds.height() - 8f,
                x + textBounds.width() + 8f,
                y + 8f
            )

            val backgroundPaint = Paint().apply {
                color = Color.BLACK
                alpha = 180
            }
            canvas.drawRoundRect(backgroundRect, 6f, 6f, backgroundPaint)

            // 绘制文字
            canvas.drawText(text, x, y, textPaint)
        }
    }
}