package com.yancao.qrscanner.viewModel

import android.app.Application
import android.content.Context
import android.graphics.Bitmap
import android.widget.Toast
import androidx.annotation.OptIn
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.google.mlkit.vision.barcode.common.Barcode
import com.yancao.qrscanner.camera.CameraManager
import com.yancao.qrscanner.domain.ScanResultsHolder


/**
 * 二维码扫描的 ViewModel
 * 负责管理摄像头操作、扫描状态和结果数据
 *
 * 功能说明：
 * - 管理摄像头的启动和停止
 * - 控制实时扫描的开启和关闭
 * - 处理拍照功能
 * - 管理扫描结果的存储和获取
 * - 提供二维码位置信息用于绘制绿框
 */
@ExperimentalGetImage
class QrScanViewModel(application: Application) : AndroidViewModel(application) {

    private val cameraManager = CameraManager(application)

    // 实时扫描结果的 LiveData
    val realtimeScanResults = MutableLiveData<List<String>>()

    // 扫描状态的 LiveData
    val isScanningEnabled = MutableLiveData(false)

    // Pair 的第一个元素是检测到的二维码列表，第二个元素是图像尺寸 (width, height)
    val qrCodePositions = MutableLiveData<Pair<List<Barcode>, Pair<Int, Int>>>()

    /**
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     * @param enableRealtimeScanning 是否启用实时扫描，默认为 false
     */
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false
    ) {
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = enableRealtimeScanning,
            onQRCodeDetected = { detectedCodes ->
                // 实时扫描回调，更新 LiveData
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 位置信息回调，用于绘制绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )
    }

    /**
     * 开始实时扫描
     * 这个方法会启用摄像头的实时二维码扫描功能
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun startRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(true)

        // 重新启动摄像头，这次启用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = true,
            onQRCodeDetected = { detectedCodes ->
                // 更新扫描结果
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 更新位置信息，用于绘制绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )
    }

    /**
     * 停止实时扫描
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun stopRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(false)

        // 重新启动摄像头，这次禁用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = false,
            onQRCodeDetected = null,
            onQRCodeWithPosition = null
        )
    }

    /**
     * 获取当前存储的所有扫描结果
     * @return 扫描结果列表
     */
    fun getAllScanResults(): List<String> {
        return ScanResultsHolder.scanResults
    }

    /**
     * 清空扫描结果
     * 这会清空全局存储的扫描结果
     */
    fun clearScanResults() {
        ScanResultsHolder.clearResults()
        realtimeScanResults.postValue(emptyList())
        // 清空位置信息
        qrCodePositions.postValue(Pair(emptyList(), Pair(0, 0)))
    }

    /**
     * 获取扫描结果数量
     * @return 当前存储的扫描结果数量
     */
    fun getScanResultCount(): Int {
        return ScanResultsHolder.getResultCount()
    }

    /**
     * 检查是否有扫描结果
     * @return 如果有扫描结果返回 true，否则返回 false
     */
    fun hasScanResults(): Boolean {
        return ScanResultsHolder.hasResults()
    }

    /**
     * 获取最新的扫描结果
     * @return 最新的扫描结果，如果没有结果返回 null
     */
    fun getLatestScanResult(): String? {
        return ScanResultsHolder.getLatestResult()
    }

    /**
     * 获取当前扫描状态
     * @return 如果正在扫描返回 true，否则返回 false
     */
    fun isCurrentlyScanning(): Boolean {
        return isScanningEnabled.value ?: false
    }

    /**
     * 切换扫描状态
     * 如果当前正在扫描则停止，如果当前停止则开始扫描
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun toggleScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        if (isCurrentlyScanning()) {
            stopRealtimeScanning(previewView, lifecycleOwner)
        } else {
            startRealtimeScanning(previewView, lifecycleOwner)
        }
    }

    /**
     * ViewModel 清理时的操作
     * 释放摄像头资源，防止内存泄漏
     */
    @OptIn(ExperimentalGetImage::class)
    override fun onCleared() {
        super.onCleared()
        cameraManager.shutdown()
    }
}