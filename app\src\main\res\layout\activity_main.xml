<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.QrScanActivity">

    <!-- 摄像头预览组件 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/button_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 二维码覆盖层 - 新增 -->
    <com.yancao.qrscanner.ui.QrCodeOverlayView
        android:id="@+id/qr_overlay_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/red_border_background"
        app:layout_constraintBottom_toBottomOf="@+id/preview_view"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintStart_toStartOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view" />

    <!-- 按钮容器，用于放置两个按钮 -->
    <LinearLayout
        android:id="@+id/button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 扫描按钮（原来的拍照按钮，改名为扫描） -->
        <Button
            android:id="@+id/btn_scan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="开始扫描"
            android:background="@android:color/holo_blue_light"
            android:textColor="@android:color/white" />

        <!-- 新增的广播按钮 -->
        <Button
            android:id="@+id/btn_broadcast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="广播结果"
            android:background="@android:color/holo_green_light"
            android:textColor="@android:color/white" />

    </LinearLayout>

    <!-- 扫描结果显示区域 -->
    <TextView
        android:id="@+id/tv_scan_results"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:background="@android:color/black"
        android:textColor="@android:color/white"
        android:padding="8dp"
        android:text="扫描结果将显示在这里..."
        android:textSize="12sp"
        android:maxLines="3"
        android:ellipsize="end"
        app:layout_constraintBottom_toTopOf="@+id/button_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>