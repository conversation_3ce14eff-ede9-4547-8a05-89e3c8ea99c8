package com.yancao.qrscanner.utils

import android.Manifest
import android.content.pm.PackageManager
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat


class RequestPermission(private val activity: ComponentActivity) {

    private val permissionLauncher = activity.registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            onPermissionGranted?.invoke()
        } else {
            onPermissionDenied?.invoke()
        }
    }

    private var onPermissionGranted: (() -> Unit)? = null
    private var onPermissionDenied: (() -> Unit)? = null

    fun checkAndRequestInstallPackagesPermission(
        onGranted: () -> Unit,
        onDenied: () -> Unit = {}
    ) {
        this.onPermissionGranted = onGranted
        this.onPermissionDenied = onDenied

        when {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.INSTALL_PACKAGES
            ) == PackageManager.PERMISSION_GRANTED -> {
                onGranted()
            }
            else -> {
                permissionLauncher.launch(Manifest.permission.INSTALL_PACKAGES)
            }
        }
    }

    fun checkAndRequestCameraPermission(
        onGranted: () -> Unit,
        onDenied: () -> Unit = {}
    ) {
        this.onPermissionGranted = onGranted
        this.onPermissionDenied = onDenied

        when {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                onGranted()
            }
            else -> {
                permissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }
}