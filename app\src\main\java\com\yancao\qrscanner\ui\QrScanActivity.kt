package com.yancao.qrscanner.ui

import android.app.Application
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.ExperimentalGetImage
import com.yancao.qrscanner.databinding.ActivityMainBinding
import com.yancao.qrscanner.domain.ScanResultsHolder
import com.yancao.qrscanner.utils.BroadCastUtils
import com.yancao.qrscanner.utils.RequestPermission
import com.yancao.qrscanner.viewModel.QrScanViewModel


@ExperimentalGetImage
class QrScanActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: QrScanViewModel by viewModels()
    private lateinit var permissionHelper: RequestPermission

    @OptIn(ExperimentalGetImage::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 新增：设置覆盖层绘制频率（可以根据需要调整）
        binding.qrOverlayView.setDrawFrequency(5) // 每5帧绘制一次

        // 初始化权限助手
        permissionHelper = RequestPermission(this)

        // 请求相机权限
        permissionHelper.checkAndRequestCameraPermission(
            onGranted = {
                // 权限获取成功，启动相机（默认不启用实时扫描）
                viewModel.startCamera(binding.previewView, this, enableRealtimeScanning = false)
            },
            onDenied = {
                Toast.makeText(this, "Camera permission denied", Toast.LENGTH_SHORT).show()
            }
        )

        // 修改：直接使用MLKit的Barcode对象
        viewModel.qrCodePositions.observe(this) { (barcodes, imageSizes) ->
            val (imageWidth, imageHeight) = imageSizes

            // 直接传递Barcode对象和图像尺寸
            binding.qrOverlayView.updateDetectedQRCodes(
                barcodes,
                imageWidth,
                imageHeight
            )
        }

        // 修改扫描状态观察，在停止扫描时清除覆盖层
        viewModel.isScanningEnabled.observe(this) { isScanning ->
            updateScanButtonText(isScanning)

            // 停止扫描时清除绿框
            if (!isScanning) {
                binding.qrOverlayView.clearDetectedQRCodes()
            }
        }

        // 设置扫描按钮点击监听
        binding.btnScan.setOnClickListener {
            handleScanButtonClick()
        }

        // 设置广播按钮点击监听（新增的按钮）
        binding.btnBroadcast.setOnClickListener {
            handleBroadcastButtonClick()
        }

        // 观察实时扫描结果
        viewModel.realtimeScanResults.observe(this) { results ->
            if (results.isNotEmpty()) {
                updateScanResultsDisplay()
            }
        }

        // 观察扫描状态
        viewModel.isScanningEnabled.observe(this) { isScanning ->
            updateScanButtonText(isScanning)
        }
    }

    /**
     * 处理扫描按钮点击事件
     * 根据当前状态决定是开始扫描还是停止扫描
     */
    @OptIn(ExperimentalGetImage::class)
    private fun handleScanButtonClick() {
        val isCurrentlyScanning = viewModel.isScanningEnabled.value ?: false

        if (isCurrentlyScanning) {
            // 当前正在扫描，点击停止扫描
            viewModel.stopRealtimeScanning(binding.previewView, this)
            Toast.makeText(this, "已停止扫描", Toast.LENGTH_SHORT).show()
        } else {
            // 当前未在扫描，点击开始扫描
            binding.tvScanResults.text = ""
            viewModel.startRealtimeScanning(binding.previewView, this)
            Toast.makeText(this, "开始扫描二维码...", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理广播按钮点击事件
     * 广播当前存储的所有扫描结果
     */
    @OptIn(ExperimentalGetImage::class)
    private fun handleBroadcastButtonClick() {
        val scanResults = viewModel.getAllScanResults()

        if (scanResults.isEmpty()) {
            Toast.makeText(this, "没有扫描结果可以广播", Toast.LENGTH_SHORT).show()
            return
        }

        // 广播所有扫描结果
        // 这里可以选择广播最新结果或所有结果，根据需求调整
        val latestResult = ScanResultsHolder.getLatestResult() ?: ""

        scanResults.forEach { result ->
            val broadcastIntent = Intent(BroadCastUtils.BROADCAST_ACTION).apply {
                putExtra(BroadCastUtils.BROADCAST_RAW_DATA, result) // 最新结果
            }
            sendBroadcast(broadcastIntent)
        }

        Toast.makeText(this, "已广播 ${scanResults.size} 个扫描结果", Toast.LENGTH_SHORT).show()
    }

    /**
     * 更新扫描结果显示
     */
    @OptIn(ExperimentalGetImage::class)
    private fun updateScanResultsDisplay() {
        val results = viewModel.getAllScanResults()
        val displayText = if (results.isEmpty()) {
            "扫描结果将显示在这里..."
        } else {
            "共扫描到 ${results.size} 个结果：${results.takeLast(2).joinToString(", ")}" +
                    if (results.size > 2) "..." else ""
        }
        binding.tvScanResults.text = displayText
    }

    /**
     * 更新扫描按钮文本
     */
    private fun updateScanButtonText(isScanning: Boolean) {
        binding.btnScan.text = if (isScanning) "停止扫描" else "开始扫描"
    }


    override fun onDestroy() {
        super.onDestroy()
        // 可以选择在Activity销毁时清空结果，或者保留给其他模块使用
         ScanResultsHolder.clearResults()
    }
}